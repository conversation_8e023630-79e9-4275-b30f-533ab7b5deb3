import { computed, ref } from "vue";
import URLFormatter from "@/services/URLFormatter";
import { useSimpleCustomFetch } from "../composables/useCustomFetch.js";
import utils from "../mixins/utils";

export const useConfigStore = defineStore("Config", () => {
  const configRef = ref(null);
  const sidebarNavsRef = ref(null);
  const featuresRef = ref({});
  const isLoadingRef = ref(true);

  const config = computed(() => configRef.value);
  const sidebarNavs = computed(() => sidebarNavsRef.value);
  const features = computed(() => featuresRef.value);

  const sidebarNavsFiltered = computed(() => {
    if (!sidebarNavsRef.value || import.meta.server) {
      return [];
    }

    const filtered = sidebarNavsRef.value
      .map((item) => ({
        ...item,
        isInSidebar: featuresRef.value[item.uniqueId] || false,
      }))
      .filter(({ isInSidebar }) => isInSidebar);

    return filtered;
  });

  const setConfigAsync = async (conf, navs) => {
    configRef.value = conf || null;
    sidebarNavsRef.value = navs || null;
    isLoadingRef.value = false;
  };

  const setFeatures = (featuresData) => {
    if (featuresData) {
      featuresRef.value = featuresData;
    }
  };

  const getConfigEndpointHost = (clientKey, endpointKey) => {
    const client = configRef.value?.clients[clientKey];
    return {
      endpoint: URLFormatter.urlFormat(client?.endpoints?.[endpointKey]),
      baseURL: client?.host,
    };
  };

  // Equivalent to Vuex getClientConfig getter
  const getClientConfig = (client) => {
    try {
      return configRef.value?.clients?.[client] || {};
    } catch (err) {
      utils.methods.printConsole("Missing client config for key: " + client);
      return {};
    }
  };

  // Equivalent to Vuex getClientEndpoint getter
  const getClientEndpoint = (client, endpoint, replaceMap = {}) => {
    try {
      let template = configRef.value?.clients?.[client]?.endpoints?.[endpoint];
      if (!template) {
        utils.methods.printConsole(
          "Missing client's endpoint config for key: " + client
        );
        return "";
      }
      return URLFormatter.urlFormat(template, replaceMap);
    } catch (e) {
      utils.methods.printConsole(
        "Missing client's endpoint config for key: " + client
      );
      return "";
    }
  };



  const fetchFeaturesAsync = async ({ isHotRefresh = false } = {}) => {
    if (Object.keys(featuresRef.value).length && !isHotRefresh) {
      return;
    }
    const { $store } = useNuxtApp();
    const project = $store?.getters['userData/getProject'];

    if (!project?.id) {
      return;
    }

    try {
      const response = await useSimpleCustomFetch('', {}, 'flite', 'getFeatureConfig');

      if (response) {
        setFeatures(response);
      }
    } catch (error) {
      console.error('[IQ][useConfigStore] Error fetching features:', error);
      throw error;
    }
  };

  return {
    configRef,
    sidebarNavsRef,
    featuresRef,
    isLoadingRef,

    config,
    sidebarNavs,
    features,
    sidebarNavsFiltered,

    setConfigAsync,
    setFeatures,
    getConfigEndpointHost,
    getClientConfig,
    getClientEndpoint,
    fetchFeaturesAsync,
  };
})
