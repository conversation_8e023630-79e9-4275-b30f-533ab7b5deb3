import { getEndpointAndBaseURL } from "./config-store-helpers.js";

/**
 * @deprecated Use getEndpointAndBaseURL from config-store-helpers.js instead
 * @param rootGetters
 * @param {string} client
 * @param {string} endpoint
 * @returns {{endpoint: *, baseURL}}
 */
export const getEndpointFromStore = (rootGetters, client, endpoint) => {
  return {
    endpoint: rootGetters["config/getClientEndpoint"](client, endpoint),
    baseURL: rootGetters["config/getClientConfig"](client).host,
  };
};

/**
 * New Pinia-based version
 * @param {string} client
 * @param {string} endpoint
 * @returns {{endpoint: *, baseURL}}
 */
export const getEndpointFromConfigStore = (client, endpoint) => {
  return getEndpointAndBaseURL(client, endpoint);
};
