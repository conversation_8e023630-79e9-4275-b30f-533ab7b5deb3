import { useConfigStore } from "../stores/config.js";

/**
 * Helper utilities to replace rootGetters patterns used in Vuex store modules
 * These functions provide a bridge for accessing config store functionality from other stores
 */

/**
 * Get client configuration for a specific client
 * Replaces: rootGetters["config/getClientConfig"](client)
 * @param {string} client - The client identifier
 * @returns {Object} The configuration object for the specified client
 */
export const getClientConfig = (client) => {
  const configStore = useConfigStore();
  return configStore.getClientConfig(client);
};

/**
 * Get client endpoint URL with optional template replacement
 * Replaces: rootGetters["config/getClientEndpoint"](client, endpoint, replaceMap)
 * @param {string} client - The client identifier
 * @param {string} endpoint - The endpoint key
 * @param {Object} replaceMap - Optional map for URL template replacement
 * @returns {string} The formatted endpoint URL
 */
export const getClientEndpoint = (client, endpoint, replaceMap = {}) => {
  const configStore = useConfigStore();
  return configStore.getClientEndpoint(client, endpoint, replaceMap);
};

/**
 * Get both endpoint and baseURL for a client and action
 * Replaces the common pattern used in store modules
 * @param {string} client - The client identifier
 * @param {string} action - The action/endpoint key
 * @returns {Object} Object with endpoint and baseURL properties
 */
export const getEndpointAndBaseURL = (client, action) => {
  const configStore = useConfigStore();
  return {
    endpoint: configStore.getClientEndpoint(client, action),
    baseURL: configStore.getClientConfig(client).host,
  };
};

/**
 * Get the configuration object
 * Replaces: rootGetters["config/getConfig"]
 * @returns {Object} The configuration object
 */
export const getConfig = () => {
  const configStore = useConfigStore();
  return configStore.config;
};

/**
 * Get the features object
 * Replaces: rootGetters["config/getFeatures"]
 * @returns {Object} The features object
 */
export const getFeatures = () => {
  const configStore = useConfigStore();
  return configStore.features;
};

/**
 * Get filtered sidebar navigation items
 * Replaces: rootGetters["config/getSidebarNavs"]
 * @returns {Array} The filtered sidebar navigation items
 */
export const getSidebarNavs = () => {
  const configStore = useConfigStore();
  return configStore.sidebarNavsFiltered;
};
