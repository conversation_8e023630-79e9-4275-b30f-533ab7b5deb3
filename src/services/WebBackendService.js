import { getFliteClient } from "./clients.js";
import { getClientEndpoint } from "../utils/config-store-helpers.js";

/**
 * @module service/WebBackendService
 */
export default {
    /**
     *
     * @param project
     * @param store
     * @param auth
     * @return {*}
     *
     * @deprecated Use action "userData/fetchLangsAsync" or getter "userData/getDefaultLang" as computed property or use watcher.
     */
    getLangConfig(project, store, auth) {
        let endpoint = getClientEndpoint("flite", "getLangConfig");
        let headers = { "X-Innit-ProjectId": project.id}
        return getFliteClient(store, auth).get(
            endpoint, { headers: headers }
        ).then(result => {
            return result.data
        })
    },

  /**
   * Retrieves feature configuration data for a specified language.
   *
   * This function sends a GET request to fetch feature configuration data using the provided language code.
   * It utilizes Vuex store getters to obtain client configuration and endpoint details,
   * along with authentication headers.
   *
   * @param {object} project - The project object containing project details.
   * @param {string} lang - The language code (e.g., "en-US") used to specify language for the request.
   * @param {object} store - The Vuex store object for accessing state and getters.
   * @param {object} auth - The authentication object for API authentication credentials.
   * @returns {Promise<object>} A promise resolving to the feature configuration data.
   * @deprecated Use getter "config/getFeatures" as computed property or use watcher.
   */
  getFeatureConfig(project, lang, store, auth) {
        let endpoint = getClientEndpoint("flite", "getFeatureConfig");
        let params = {};
        params.lang = lang;
        let headers = { "X-Innit-ProjectId": project.id}
        return getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
};
