/**
 * @module store/recipePreview
 */
import { KEYS } from '@/сonstants/keys';
import { useNuxtApp } from "nuxt/app";
import { getEndpointAndBaseURL } from "../utils/config-store-helpers.js";

const state = () => ({
  recipeServingScaleList: [],
});

const createMutation = (property) => {
  return (state, value) => {
    state[property] = value;
  };
};

const mutations = {
  SET_RECIPE_SERVING_SCALE: createMutation("recipeServingScaleList"),
};

const actions = {
  async getRecipeServingScaleAsync({ commit }, { payload, lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }

    try {
      const { endpoint, baseURL } = getEndpointAndBaseURL("fss", "getServingScale");
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params: { country: lang.split('-')[1] },
      });
      if (response) {
        commit("SET_RECIPE_SERVING_SCALE", response.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getRecipeServingScaleAsync", error);
    }
  },
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getRecipeServingScale: createGetter("recipeServingScaleList"),
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
