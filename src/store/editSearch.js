import { useNuxtApp } from "nuxt/app";
import { getEndpointAndBaseURL } from "../utils/config-store-helpers.js";

const state = () => ({
  editSearch: {},
});

const mutations = {
  SET_EDIT_SEARCH(state, value) {
    state.editSearch = value;
  },
};

const actions = {
  async getEditSearchAsync({ commit }) {
    try {
      const { endpoint, baseURL } = getEndpointAndBaseURL("flite", "getEditSearch");
      const { $axios } = useNuxtApp();

      const response = await $axios.get(endpoint, {
        baseURL,
      });

      if (response) {
        commit("SET_EDIT_SEARCH", response.data);
      }
    } catch (error) {
      console.error(
        this.$keys.KEY_NAMES.ERROR_IN + "getEditSearchAsync:",
        error
      );
    }
  },

  async postEditSearchAsync({ }, { payload, user }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(payload).length || !user) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const { endpoint, baseURL } = getEndpointAndBaseURL("flite", "getEditSearch");
      let params = {};
      params.user = user;
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params,
      });
      return response.data;
    } catch (error) {
      console.error(
        this.$keys.KEY_NAMES.ERROR_IN + "postEditSearchAsync:",
        error
      );
    }
  },
};

const getters = {
  getEditSearch: (state) => state.editSearch,
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
